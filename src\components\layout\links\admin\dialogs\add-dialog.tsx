import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { LinksForm } from '@/components/layout/links/admin/links-form';
import { useLinksStore } from '@/stores/links.store';
import type { LinksFormData } from '@/lib/schemas/links.schema';

interface AddDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  onError?: (message: string) => void;
}

export function AddDialog({ open, onOpenChange, onSuccess, onError }: AddDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addItem } = useLinksStore();

  const handleSubmit = async (formData: LinksFormData) => {
    setIsSubmitting(true);
    try {
      await addItem(formData);
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error adding item:', error);
      if (error instanceof Error && error.message.includes('URL already exists')) {
        onError?.('该网址已存在');
      } else {
        onError?.('添加网址失败');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto space-y-6">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">添加新网址</DialogTitle>
        </DialogHeader>
        <LinksForm
          submitAction={handleSubmit}
          onCancel={() => onOpenChange(false)}
          isLoading={isSubmitting}
        />
      </DialogContent>
    </Dialog>
  );
}
