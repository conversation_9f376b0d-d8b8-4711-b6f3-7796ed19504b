import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import matter from 'gray-matter';
import { z } from 'zod';
import { TOOLS } from '@/components/layout/tools/tools-data';
import items from '@/data/links/items.json';
import type { Item } from '@/types/links';

// Zod Schemas
const SearchParamsSchema = z.object({
  q: z.string().min(1, '搜索查询不能为空'),
  type: z.enum(['doc', 'blog', 'tool', 'link']).optional(),
  limit: z.coerce.number().int().positive().optional().default(8),
});

const SearchResultSchema = z.object({
  title: z.string(),
  path: z.string(),
  excerpt: z.string(),
  type: z.enum(['doc', 'blog', 'tool', 'link']),
  score: z.number(),
  highlights: z
    .object({
      title: z.string().optional(),
      content: z.array(z.string()).optional(),
    })
    .optional(),
});

type SearchResult = z.infer<typeof SearchResultSchema>;

// 辅助函数
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function highlightText(text: string, query: string): string {
  const regex = new RegExp(`(${escapeRegExp(query)})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

function findContentMatches(content: string, query: string, maxMatches: number = 3): string[] {
  const lowerContent = content.toLowerCase();
  const lowerQuery = query.toLowerCase();
  const matches: string[] = [];

  let startIndex = 0;
  while (matches.length < maxMatches) {
    const index = lowerContent.indexOf(lowerQuery, startIndex);
    if (index === -1) break;

    const start = Math.max(0, index - 50);
    const end = Math.min(content.length, index + query.length + 50);
    let context = content.substring(start, end);

    if (start > 0) context = '...' + context;
    if (end < content.length) context = context + '...';

    matches.push(highlightText(context, query));
    startIndex = index + query.length;
  }

  return matches;
}

// 递归读取目录下的所有 .md 和 .mdx 文件
async function getAllFiles(dirPath: string): Promise<string[]> {
  const entries = await fs.readdir(dirPath, { withFileTypes: true });
  const files = await Promise.all(
    entries.map(entry => {
      const res = path.resolve(dirPath, entry.name);
      return entry.isDirectory() ? getAllFiles(res) : res;
    })
  );
  return files.flat().filter(file => /\.(md|mdx)$/.test(file));
}

async function searchDocs(query: string): Promise<SearchResult[]> {
  const docsDir = path.join(process.cwd(), 'src/content/docs');
  const files = await getAllFiles(docsDir);
  const results: SearchResult[] = [];

  for (const file of files) {
    try {
      const content = await fs.readFile(file, 'utf-8');
      const { data, content: markdown } = matter(content);
      const relativePath = path.relative(docsDir, file);
      const url = `/docs/${relativePath.replace(/\.(md|mdx)$/, '')}`;

      let score = 0;
      const highlights: SearchResult['highlights'] = {};

      if (data.title?.toLowerCase().includes(query.toLowerCase())) {
        score += 10;
        highlights.title = highlightText(data.title, query);
      }
      if (data.description?.toLowerCase().includes(query.toLowerCase())) {
        score += 5;
      }
      const contentMatches = findContentMatches(markdown, query);
      if (contentMatches.length > 0) {
        score += contentMatches.length;
        highlights.content = contentMatches;
      }

      if (score > 0) {
        results.push({
          title: data.title || '',
          path: url,
          excerpt: data.description || markdown.slice(0, 160) + '...',
          type: 'doc',
          score,
          highlights,
        });
      }
    } catch (error) {
      console.error(`Error parsing frontmatter for file: ${file}`, error);
      continue;
    }
  }
  return results;
}

async function searchBlog(query: string): Promise<SearchResult[]> {
  const blogDir = path.join(process.cwd(), 'src/content/blog');
  const files = await getAllFiles(blogDir);
  const results: SearchResult[] = [];

  for (const file of files) {
    try {
      const content = await fs.readFile(file, 'utf-8');
      const { data, content: markdown } = matter(content);
      const relativePath = path.relative(blogDir, file);
      const url = `/blog/${relativePath.replace(/\.(md|mdx)$/, '')}`;

      let score = 0;
      const highlights: SearchResult['highlights'] = {};

      if (data.title?.toLowerCase().includes(query.toLowerCase())) {
        score += 10;
        highlights.title = highlightText(data.title, query);
      }
      if (data.description?.toLowerCase().includes(query.toLowerCase())) {
        score += 5;
      }
      if (data.tags?.some((tag: string) => tag.toLowerCase().includes(query.toLowerCase()))) {
        score += 3;
      }
      const contentMatches = findContentMatches(markdown, query);
      if (contentMatches.length > 0) {
        score += contentMatches.length;
        highlights.content = contentMatches;
      }

      if (score > 0) {
        results.push({
          title: data.title || '',
          path: url,
          excerpt: data.description || markdown.slice(0, 160) + '...',
          type: 'blog',
          score,
          highlights,
        });
      }
    } catch (error) {
      console.error(`Error parsing frontmatter for file: ${file}`, error);
      continue;
    }
  }
  return results;
}

function searchTools(query: string): SearchResult[] {
  return TOOLS.map(tool => {
    let score = 0;
    const highlights: SearchResult['highlights'] = {};
    if (tool.name.toLowerCase().includes(query.toLowerCase())) {
      score += 10;
      highlights.title = highlightText(tool.name, query);
    }
    if (tool.description.toLowerCase().includes(query.toLowerCase())) {
      score += 5;
      highlights.content = [highlightText(tool.description, query)];
    }
    if (tool.tags.some((tag: string) => tag.toLowerCase().includes(query.toLowerCase()))) {
      score += 3;
    }
    return {
      title: tool.name,
      path: tool.path,
      excerpt: tool.description,
      type: 'tool' as const,
      score,
      highlights,
    };
  }).filter(result => result.score > 0);
}

function searchLinks(query: string): SearchResult[] {
  return (items as Item[])
    .map(item => {
      let score = 0;
      const highlights: SearchResult['highlights'] = {};
      if (item.title.toLowerCase().includes(query.toLowerCase())) {
        score += 10;
        highlights.title = highlightText(item.title, query);
      }
      if (item.description.toLowerCase().includes(query.toLowerCase())) {
        score += 5;
        highlights.content = [highlightText(item.description, query)];
      }
      if (item.tags.some((tag: string) => tag.toLowerCase().includes(query.toLowerCase()))) {
        score += 3;
      }
      return {
        title: item.title,
        path: item.url,
        excerpt: item.description,
        type: 'link' as const,
        score,
        highlights,
      };
    })
    .filter(result => result.score > 0);
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = Object.fromEntries(request.nextUrl.searchParams);
    const validation = SearchParamsSchema.safeParse(searchParams);

    if (!validation.success) {
      return NextResponse.json({ error: validation.error.flatten() }, { status: 400 });
    }

    const { q: query, type, limit } = validation.data;

    let results: SearchResult[] = [];

    const searchTasks: Promise<SearchResult[]>[] = [];
    if (!type || type === 'doc') searchTasks.push(searchDocs(query));
    if (!type || type === 'blog') searchTasks.push(searchBlog(query));
    if (!type || type === 'tool') searchTasks.push(Promise.resolve(searchTools(query)));
    if (!type || type === 'link') searchTasks.push(Promise.resolve(searchLinks(query)));

    results = (await Promise.all(searchTasks)).flat();

    const finalResults = results.sort((a, b) => b.score - a.score).slice(0, limit);

    const finalValidation = z.array(SearchResultSchema).safeParse(finalResults);

    if (!finalValidation.success) {
      console.error('Search result validation failed:', finalValidation.error);
      return NextResponse.json(
        { error: 'Internal server error: Invalid search result format' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      results: finalValidation.data,
      total: finalValidation.data.length,
    });
  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
