/**
 * 统一的链接数据管理服务
 * 提供一致的数据访问接口，支持多种存储后端
 * 集成 Zod 验证和 Zustand 状态管理
 */

import {
  LinksData,
  LinksItem,
  LinksCategory,
  CreateLinksItem,
  LinksFormData,
  validateLinksData,
  validateLinksItem,
  validateCreateLinksItem,
  validateLinksFormData,
  LinksDataSchema,
} from '@/lib/schemas/links.schema';

// 存储类型
export type StorageType = 'file' | 'memory' | 'database';

// 存储接口
export interface LinksStorage {
  read(): Promise<LinksData>;
  write(data: LinksData): Promise<void>;
  addItem(item: CreateLinksItem): Promise<LinksItem>;
  updateItem(id: string, updates: Partial<LinksFormData>): Promise<LinksItem>;
  deleteItem(id: string): Promise<void>;
  getCategories(): Promise<LinksCategory[]>;
  getAllTags(): Promise<string[]>;
  validateData(data: unknown): { success: boolean; data?: LinksData; error?: string };
}

// 文件存储实现
export class FileLinksStorage implements LinksStorage {
  async read(): Promise<LinksData> {
    try {
      const { readLinksData } = await import('@/components/layout/links/admin/links-manage');
      const raw = readLinksData();

      // 使用 Zod 验证和转换数据
      const validation = validateLinksData(raw);
      if (!validation.success) {
        console.error('Links data validation failed:', validation.error);
        // 返回空数据而不是抛出错误
        return { categories: [], items: [] };
      }

      return validation.data;
    } catch (error) {
      console.error('Error reading links data:', error);
      return { categories: [], items: [] };
    }
  }

  async write(data: LinksData): Promise<void> {
    try {
      // 验证数据
      const validation = validateLinksData(data);
      if (!validation.success) {
        throw new Error(`Invalid links data: ${validation.error?.message}`);
      }

      // 转换为旧类型格式以兼容现有的 writeLinksData 函数
      const convertedData = {
        categories: validation.data.categories.map(cat => ({
          id: cat.id as any,
          name: cat.name,
          description: cat.description || '',
          order: cat.order,
          icon: cat.icon,
          color: cat.color,
        })),
        items: validation.data.items.map(item => ({
          id: item.id,
          title: item.title,
          description: item.description,
          url: item.url,
          icon: item.icon,
          iconType: item.iconType as any,
          tags: item.tags,
          featured: item.featured,
          category: item.category as any,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          visits: item.visits,
          isActive: item.isActive,
        })),
      };

      const { writeLinksData } = await import('@/components/layout/links/admin/links-manage');
      writeLinksData(convertedData as any);
    } catch (error) {
      console.error('Error writing links data:', error);
      throw new Error('Failed to write links data');
    }
  }

  async addItem(item: CreateLinksItem): Promise<LinksItem> {
    try {
      // 验证输入数据
      const validation = validateCreateLinksItem(item);
      if (!validation.success) {
        throw new Error(`Invalid item data: ${validation.error?.message}`);
      }

      const { addLinksItem } = await import('@/components/layout/links/admin/links-manage');
      const result = addLinksItem(validation.data);

      // 验证返回的数据
      const resultValidation = validateLinksItem(result);
      if (!resultValidation.success) {
        throw new Error('Invalid result from addLinksItem');
      }

      return resultValidation.data;
    } catch (error) {
      console.error('Error adding item:', error);
      throw error;
    }
  }

  async updateItem(id: string, updates: Partial<LinksFormData>): Promise<LinksItem> {
    try {
      // 验证更新数据（部分字段）
      const validation = validateLinksFormData(updates);
      if (!validation.success) {
        throw new Error(`Invalid update data: ${validation.error?.message}`);
      }

      const { updateLinksItem } = await import('@/components/layout/links/admin/links-manage');
      const result = updateLinksItem(id, validation.data);

      // 验证返回的数据
      const resultValidation = validateLinksItem(result);
      if (!resultValidation.success) {
        throw new Error('Invalid result from updateLinksItem');
      }

      return resultValidation.data;
    } catch (error) {
      console.error('Error updating item:', error);
      throw error;
    }
  }

  async deleteItem(id: string): Promise<void> {
    const { deleteLinksItem } = await import('@/components/layout/links/admin/links-manage');
    deleteLinksItem(id);
  }

  async getCategories(): Promise<LinksCategory[]> {
    try {
      const { getCategories } = await import('@/components/layout/links/admin/links-manage');
      const raw = getCategories();

      // 验证分类数据
      const validation = LinksDataSchema.pick({ categories: true }).safeParse({ categories: raw });
      if (!validation.success) {
        console.error('Categories validation failed:', validation.error);
        return [];
      }

      return validation.data.categories;
    } catch (error) {
      console.error('Error getting categories:', error);
      return [];
    }
  }

  async getAllTags(): Promise<string[]> {
    try {
      const { getAllTags } = await import('@/components/layout/links/admin/links-manage');
      return getAllTags();
    } catch (error) {
      console.error('Error getting tags:', error);
      return [];
    }
  }

  validateData(data: unknown): { success: boolean; data?: LinksData; error?: string } {
    const validation = validateLinksData(data);
    return {
      success: validation.success,
      data: validation.success ? validation.data : undefined,
      error: validation.success ? undefined : validation.error?.message,
    };
  }
}

// 内存存储实现（用于测试）
export class MemoryLinksStorage implements LinksStorage {
  private data: LinksData = { categories: [], items: [] };

  async read(): Promise<LinksData> {
    return { ...this.data };
  }

  async write(data: LinksData): Promise<void> {
    this.data = { ...data };
  }

  async addItem(item: CreateLinksItem): Promise<LinksItem> {
    // 验证输入数据
    const validation = validateCreateLinksItem(item);
    if (!validation.success) {
      throw new Error(`Invalid item data: ${validation.error?.message}`);
    }

    const newItem: LinksItem = {
      ...validation.data,
      id: `item_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // 验证创建的项目
    const itemValidation = validateLinksItem(newItem);
    if (!itemValidation.success) {
      throw new Error('Failed to create valid item');
    }

    this.data.items.push(itemValidation.data);
    return itemValidation.data;
  }

  async updateItem(id: string, updates: Partial<LinksFormData>): Promise<LinksItem> {
    const itemIndex = this.data.items.findIndex(item => item.id === id);
    if (itemIndex === -1) {
      throw new Error('Links item not found');
    }

    // 验证更新数据
    const validation = validateLinksFormData(updates);
    if (!validation.success) {
      throw new Error(`Invalid update data: ${validation.error?.message}`);
    }

    const updatedItem: LinksItem = {
      ...this.data.items[itemIndex],
      ...validation.data,
      id,
      updatedAt: new Date().toISOString(),
    };

    // 验证更新后的项目
    const itemValidation = validateLinksItem(updatedItem);
    if (!itemValidation.success) {
      throw new Error('Failed to create valid updated item');
    }

    this.data.items[itemIndex] = itemValidation.data;
    return updatedItem;
  }

  async deleteItem(id: string): Promise<void> {
    const itemIndex = this.data.items.findIndex(item => item.id === id);
    if (itemIndex === -1) {
      throw new Error('Links item not found');
    }

    this.data.items.splice(itemIndex, 1);
  }

  async getCategories(): Promise<LinksCategory[]> {
    return this.data.categories.map(cat => ({
      ...cat,
      count: this.data.items.filter(item => item.category === cat.id).length,
    }));
  }

  async getAllTags(): Promise<string[]> {
    const tags = new Set<string>();
    this.data.items.forEach(item => {
      item.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags).sort();
  }

  validateData(data: unknown): { success: boolean; data?: LinksData; error?: string } {
    const validation = validateLinksData(data);
    return {
      success: validation.success,
      data: validation.success ? validation.data : undefined,
      error: validation.success ? undefined : validation.error?.message,
    };
  }
}

// 链接服务类
export class LinksService {
  private storage: LinksStorage;

  constructor(storageType: StorageType = 'file') {
    switch (storageType) {
      case 'file':
        this.storage = new FileLinksStorage();
        break;
      case 'memory':
        this.storage = new MemoryLinksStorage();
        break;
      case 'database':
        // TODO: 实现数据库存储
        throw new Error('Database storage not implemented yet');
      default:
        throw new Error(`Unknown storage type: ${storageType}`);
    }
  }

  // 获取所有数据
  async getData(): Promise<LinksData> {
    return this.storage.read();
  }

  // 添加链接
  async addLink(item: Omit<LinksItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<LinksItem> {
    return this.storage.addItem(item);
  }

  // 更新链接
  async updateLink(id: string, updates: Partial<LinksItem>): Promise<LinksItem> {
    return this.storage.updateItem(id, updates);
  }

  // 删除链接
  async deleteLink(id: string): Promise<void> {
    return this.storage.deleteItem(id);
  }

  // 获取分类
  async getCategories(): Promise<LinksCategory[]> {
    return this.storage.getCategories();
  }

  // 获取所有标签
  async getAllTags(): Promise<string[]> {
    return this.storage.getAllTags();
  }

  // 按分类获取链接
  async getLinksByCategory(categoryId: string): Promise<LinksItem[]> {
    const data = await this.getData();
    return data.items.filter(item => item.category === categoryId);
  }

  // 按标签获取链接
  async getLinksByTag(tag: string): Promise<LinksItem[]> {
    const data = await this.getData();
    return data.items.filter(item => item.tags.includes(tag));
  }

  // 搜索链接
  async searchLinks(query: string): Promise<LinksItem[]> {
    const data = await this.getData();
    const lowerQuery = query.toLowerCase();

    return data.items.filter(
      item =>
        item.title.toLowerCase().includes(lowerQuery) ||
        item.description.toLowerCase().includes(lowerQuery) ||
        item.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  // 获取推荐链接
  async getFeaturedLinks(): Promise<LinksItem[]> {
    const data = await this.getData();
    return data.items.filter(item => item.featured);
  }

  // 验证链接数据
  validateLinkData(item: Partial<LinksItem>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!item.title?.trim()) {
      errors.push('标题不能为空');
    }

    if (!item.url?.trim()) {
      errors.push('URL 不能为空');
    } else {
      try {
        new URL(item.url);
      } catch {
        errors.push('URL 格式不正确');
      }
    }

    if (!item.category?.trim()) {
      errors.push('分类不能为空');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

// 默认服务实例
export const linksService = new LinksService('file');

// 导出类型
export type { LinksData, LinksItem, LinksCategory };
