import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import {
  LinksItem,
  LinksCategory,
  LinksData,
  CategoryId,
  LinksFormData,
  validateLinksItem,
  validateLinksFormData,
} from '@/lib/schemas/links.schema';

/**
 * 过滤状态接口
 */
interface FilterState {
  selectedCategory: CategoryId | null;
  selectedTag: string | null;
  searchQuery: string;
  showFeaturedOnly: boolean;
}

/**
 * 加载状态接口
 */
interface LoadingState {
  isLoading: boolean;
  isAdding: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
}

/**
 * Links Store 状态接口
 */
interface LinksState {
  // 数据状态
  items: LinksItem[];
  categories: LinksCategory[];

  // 过滤状态
  filter: FilterState;

  // 加载状态
  loading: LoadingState;

  // 计算属性
  filteredItems: LinksItem[];
  availableTags: string[];

  // 数据操作方法
  setData: (data: LinksData) => void;
  loadData: () => Promise<void>;

  // 链接项操作
  addItem: (formData: LinksFormData) => Promise<LinksItem | null>;
  updateItem: (id: string, updates: Partial<LinksFormData>) => Promise<LinksItem | null>;
  deleteItem: (id: string) => Promise<boolean>;

  // 过滤操作
  setSelectedCategory: (categoryId: CategoryId | null) => void;
  setSelectedTag: (tag: string | null) => void;
  setSearchQuery: (query: string) => void;
  setShowFeaturedOnly: (show: boolean) => void;
  clearFilters: () => void;

  // 工具方法
  getCategoryName: (categoryId: CategoryId) => string;
  getItemsByCategory: (categoryId: CategoryId) => LinksItem[];
  getItemsByTag: (tag: string) => LinksItem[];
  getFeaturedItems: () => LinksItem[];

  // 错误处理
  setError: (error: string | null) => void;
  clearError: () => void;
}

/**
 * 默认过滤状态
 */
const defaultFilterState: FilterState = {
  selectedCategory: null,
  selectedTag: null,
  searchQuery: '',
  showFeaturedOnly: false,
};

/**
 * 默认加载状态
 */
const defaultLoadingState: LoadingState = {
  isLoading: false,
  isAdding: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
};

/**
 * 过滤链接项的工具函数
 */
const filterItems = (items: LinksItem[], filter: FilterState): LinksItem[] => {
  return items.filter(item => {
    // 分类过滤
    if (filter.selectedCategory && item.category !== filter.selectedCategory) {
      return false;
    }

    // 标签过滤
    if (filter.selectedTag && !item.tags.includes(filter.selectedTag)) {
      return false;
    }

    // 特色过滤
    if (filter.showFeaturedOnly && !item.featured) {
      return false;
    }

    // 搜索过滤
    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase();
      return (
        item.title.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query) ||
        item.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return true;
  });
};

/**
 * 获取所有可用标签的工具函数
 */
const extractTags = (items: LinksItem[]): string[] => {
  const tagSet = new Set<string>();
  items.forEach(item => {
    item.tags.forEach(tag => tagSet.add(tag));
  });
  return Array.from(tagSet).sort();
};

/**
 * 创建 Links Store
 */
export const useLinksStore = create<LinksState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // 初始状态
      items: [],
      categories: [],
      filter: defaultFilterState,
      loading: defaultLoadingState,

      // 计算属性
      get filteredItems() {
        const { items, filter } = get();
        return filterItems(items, filter);
      },

      get availableTags() {
        const { items } = get();
        return extractTags(items);
      },

      // 数据操作方法
      setData: (data: LinksData) => {
        set(state => {
          state.items = data.items;
          state.categories = data.categories;
          state.loading.error = null;
        });
      },

      loadData: async () => {
        console.log('🔄 Store: Starting loadData...');
        set(state => {
          state.loading.isLoading = true;
          state.loading.error = null;
        });

        try {
          console.log('📡 Store: Fetching /api/links...');
          const response = await fetch('/api/links');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          console.log('📦 Store: Received data:', {
            itemsCount: data.items?.length || 0,
            categoriesCount: data.categories?.length || 0,
            firstItem: data.items?.[0]?.title || 'No items'
          });

          set(state => {
            state.items = data.items || [];
            state.categories = data.categories || [];
            state.loading.isLoading = false;
          });
          console.log('✅ Store: Data loaded successfully');
        } catch (error) {
          console.log('❌ Store: Error loading data:', error);
          set(state => {
            state.loading.isLoading = false;
            state.loading.error = error instanceof Error ? error.message : '加载数据失败';
          });
        }
      },

      // 链接项操作
      addItem: async (formData: LinksFormData) => {
        const validation = validateLinksFormData(formData);
        if (!validation.success) {
          set(state => {
            state.loading.error = validation.error.errors[0]?.message || '数据验证失败';
          });
          return null;
        }

        set(state => {
          state.loading.isAdding = true;
          state.loading.error = null;
        });

        try {
          const response = await fetch('/api/links', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '添加失败');
          }

          const newItem = await response.json();
          const itemValidation = validateLinksItem(newItem);

          if (!itemValidation.success) {
            throw new Error('服务器返回的数据格式不正确');
          }

          set(state => {
            state.items.push(itemValidation.data);
            state.loading.isAdding = false;
          });

          return itemValidation.data;
        } catch (error) {
          set(state => {
            state.loading.isAdding = false;
            state.loading.error = error instanceof Error ? error.message : '添加失败';
          });
          return null;
        }
      },

      updateItem: async (id: string, updates: Partial<LinksFormData>) => {
        set(state => {
          state.loading.isUpdating = true;
          state.loading.error = null;
        });

        try {
          const response = await fetch(`/api/links?id=${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updates),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '更新失败');
          }

          const updatedItem = await response.json();
          const itemValidation = validateLinksItem(updatedItem);

          if (!itemValidation.success) {
            throw new Error('服务器返回的数据格式不正确');
          }

          set(state => {
            const index = state.items.findIndex(item => item.id === id);
            if (index !== -1) {
              state.items[index] = itemValidation.data;
            }
            state.loading.isUpdating = false;
          });

          return itemValidation.data;
        } catch (error) {
          set(state => {
            state.loading.isUpdating = false;
            state.loading.error = error instanceof Error ? error.message : '更新失败';
          });
          return null;
        }
      },

      deleteItem: async (id: string) => {
        set(state => {
          state.loading.isDeleting = true;
          state.loading.error = null;
        });

        try {
          const response = await fetch(`/api/links?id=${id}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '删除失败');
          }

          set(state => {
            state.items = state.items.filter(item => item.id !== id);
            state.loading.isDeleting = false;
          });

          return true;
        } catch (error) {
          set(state => {
            state.loading.isDeleting = false;
            state.loading.error = error instanceof Error ? error.message : '删除失败';
          });
          return false;
        }
      },

      // 过滤操作
      setSelectedCategory: (categoryId: CategoryId | null) => {
        set(state => {
          state.filter.selectedCategory = categoryId;
        });
      },

      setSelectedTag: (tag: string | null) => {
        set(state => {
          state.filter.selectedTag = tag;
        });
      },

      setSearchQuery: (query: string) => {
        set(state => {
          state.filter.searchQuery = query;
        });
      },

      setShowFeaturedOnly: (show: boolean) => {
        set(state => {
          state.filter.showFeaturedOnly = show;
        });
      },

      clearFilters: () => {
        set(state => {
          state.filter = { ...defaultFilterState };
        });
      },

      // 工具方法
      getCategoryName: (categoryId: CategoryId) => {
        const { categories } = get();
        const category = categories.find(cat => cat.id === categoryId);
        return category?.name || categoryId;
      },

      getItemsByCategory: (categoryId: CategoryId) => {
        const { items } = get();
        return items.filter(item => item.category === categoryId);
      },

      getItemsByTag: (tag: string) => {
        const { items } = get();
        return items.filter(item => item.tags.includes(tag));
      },

      getFeaturedItems: () => {
        const { items } = get();
        return items.filter(item => item.featured);
      },

      // 错误处理
      setError: (error: string | null) => {
        set(state => {
          state.loading.error = error;
        });
      },

      clearError: () => {
        set(state => {
          state.loading.error = null;
        });
      },
    }))
  )
);

// 导出选择器 hooks
export const useLinksItems = () => useLinksStore(state => state.items);
export const useLinksCategories = () => useLinksStore(state => state.categories);
export const useFilteredItems = () => useLinksStore(state => state.filteredItems);
export const useLinksFilter = () => useLinksStore(state => state.filter);
export const useLinksLoading = () => useLinksStore(state => state.loading);
export const useAvailableTags = () => useLinksStore(state => state.availableTags);
