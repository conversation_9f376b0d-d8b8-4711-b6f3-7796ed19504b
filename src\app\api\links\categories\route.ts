import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { LinksDataSchema } from '@/lib/schemas/links.schema';

const filePath = path.join(process.cwd(), 'src/data/links/categories.json');

export async function GET() {
  try {
    const data = await fs.readFile(filePath, 'utf-8');
    const categories = JSON.parse(data);

    // 验证分类数据
    const validation = LinksDataSchema.pick({ categories: true }).safeParse({ categories });
    if (!validation.success) {
      console.error('Categories data validation failed:', validation.error);
      return NextResponse.json({ error: 'Invalid categories data format' }, { status: 500 });
    }

    return NextResponse.json(validation.data.categories);
  } catch (error) {
    console.error('GET /api/links/categories error:', error);
    return NextResponse.json({ error: 'Failed to read categories' }, { status: 500 });
  }
}
