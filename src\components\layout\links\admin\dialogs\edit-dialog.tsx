import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { LinksForm } from '@/components/layout/links/admin/links-form';
import { useLinksStore } from '@/stores/links.store';
import type { LinksItem, LinksFormData } from '@/lib/schemas/links.schema';

interface EditDialogProps {
  item: LinksItem | null;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  onError?: (message: string) => void;
}

export function EditDialog({ item, onOpenChange, onSuccess, onError }: EditDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { updateItem } = useLinksStore();

  const handleSubmit = async (formData: LinksFormData) => {
    if (!item) return;

    setIsSubmitting(true);
    try {
      await updateItem(item.id, formData);
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating item:', error);
      if (error instanceof Error) {
        onError?.(error.message);
      } else {
        onError?.('更新网址失败');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={!!item} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>编辑网址</DialogTitle>
        </DialogHeader>
        {item && (
          <LinksForm
            submitAction={handleSubmit}
            onCancel={() => onOpenChange(false)}
            initialData={item}
            isLoading={isSubmitting}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
