---
title: 使用 Zod + Zustand 的项目重构方案 (详细版)
description: 一份针对本项目的、具体到文件和步骤的详细重构指南，用于全面引入 Zod 和 Zustand。
---

## 第二阶段：详细重构清单

以下是按功能模块划分的具体重构任务。

### 模块三：链接 (Links)

#### 1. API 验证 (Zod)

-   **目标**: 验证链接的创建和分类查询。
-   **文件**:
    -   `src/lib/schemas/links.schema.ts` (新建)
    -   `src/app/api/links/route.ts` (修改)
    -   `src/app/api/links/categories/route.ts` (修改)
-   **步骤**:
    1.  在 `src/lib/schemas/links.schema.ts` 中定义模式：
        ```typescript
        import { z } from 'zod';

        export const LinkSchema = z.object({
          id: z.string(),
          url: z.string().url(),
          title: z.string(),
          description: z.string().optional(),
          category: z.string(),
        });

        export const CreateLinkSchema = LinkSchema.omit({ id: true });
        ```
    2.  在 `src/app/api/links/route.ts` 的 `POST` 方法中，使用 `CreateLinkSchema` 验证请求体。

#### 2. 表单重构 (Zod + React Hook Form)

-   **目标**: 使用 `react-hook-form` 和 Zod 重构添加/编辑链接的表单。
-   **文件**:
    -   `src/components/layout/links/admin/dialogs/AddLinkDialog.tsx` (假设存在，修改)
-   **步骤**:
    1.  使用 `useForm` hook 并传入 `zodResolver(CreateLinkSchema)`。
    2.  将表单 `input` 与 `register` 方法绑定。
    3.  通过 `formState.errors` 显示验证错误信息。

#### 3. 状态管理 (Zustand)

-   **目标**: 使用 Zustand 管理链接页面的筛选和搜索状态。
-   **文件**:
    -   `src/stores/links.store.ts` (新建)
    -   `src/app/links/page.tsx` (修改)
    -   `src/components/common/filter/FilterComponent.tsx` (假设存在，修改)
-   **步骤**:
    1.  在 `src/stores/links.store.ts` 中创建 `useLinksFilterStore`：
        ```typescript
        import { create } from 'zustand';

        type LinksFilterState = {
          category: string | null;
          searchQuery: string;
          setCategory: (category: string | null) => void;
          setSearchQuery: (query: string) => void;
        };

        export const useLinksFilterStore = create<LinksFilterState>((set) => ({
          category: null,
          searchQuery: '',
          setCategory: (category) => set({ category }),
          setSearchQuery: (query) => set({ searchQuery: query }),
        }));
        ```
    2.  在 `FilterComponent` 中，使用 `useLinksFilterStore` 的 `set` 方法来更新筛选条件。
    3.  在 `src/app/links/page.tsx` 中，使用 `useLinksFilterStore` 获取筛选条件，并据此获取和展示数据。

### 模块四：工具 (Tools)

-   **目标**: 对每个交互式工具的状态管理进行审查，并用 Zustand 优化。
-   **示例文件**: `src/app/tools/timestamp-converter/page.tsx`
-   **步骤**:
    1.  **识别状态**: 识别该工具中的所有状态，例如 `inputValue`、`outputValue`、`format` 等。
    2.  **创建 Store**: 创建一个 `src/stores/timestamp-converter.store.ts`。
        ```typescript
        import { create } from 'zustand';

        type ConverterState = {
          input: string;
          output: string;
          // ... other states and actions
          setInput: (input: string) => void;
        };
        // ...
        ```
    3.  **重构组件**: 在 `page.tsx` 中，用 `useTimestampConverterStore()` 替换所有的 `useState`，将业务逻辑移入 store 的 actions 中。

---

## 第三阶段：测试与清理

此阶段的目标是确保重构的正确性并清理遗留代码。

-   **更新测试**: 为所有新建的 Zod schemas 和 Zustand stores 编写单元测试。
-   **代码清理**:
    -   安全地删除所有被替换的 `Context`、`Reducer` 和手写的验证函数。
    -   用 `z.infer<typeof ...>` 替换所有可以被推断的、手写的 TypeScript `type` 或 `interface`。
    -   审查 `package.json`，移除不再需要的依赖
通过这份详细的清单，我们可以有条不紊地对项目进行现代化改造，确保每一步都有明确的目标和路径。
