'use client';

import React, { useState, useEffect } from 'react';
import { useDebouncedValue } from '@/hooks/use-debounced-value';
import { AdminLayout } from '@/components/layout/admin/admin-layout';
import { AdminActions } from '@/components/layout/admin/admin-actions';
import { DataTable } from '@/components/layout/links/admin/data-table';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Search, Globe } from 'lucide-react';
import { AddDialog } from '@/components/layout/links/admin/dialogs/add-dialog';
import { EditDialog } from '@/components/layout/links/admin/dialogs/edit-dialog';
import { DeleteDialog } from '@/components/layout/links/admin/dialogs/delete-dialog';
import {
  getTableColumns,
  getTableActions,
  getPageActions,
} from '@/components/layout/links/admin/table-config';
import { useLinksStore } from '@/stores/links.store';
import type { LinksItem, CategoryId } from '@/lib/schemas/links.schema';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
export default function LinksAdminPage() {
  const {
    items,
    categories,
    filteredItems,
    loadData,
    setSearchQuery,
    setSelectedCategory: setStoreSelectedCategory,
  } = useLinksStore();

  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebouncedValue(searchTerm, 300);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<LinksItem | null>(null);
  const [deletingItem, setDeletingItem] = useState<LinksItem | null>(null);

  // 加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 同步搜索和分类过滤到 store
  useEffect(() => {
    setSearchQuery(debouncedSearchTerm);
  }, [debouncedSearchTerm, setSearchQuery]);

  useEffect(() => {
    setStoreSelectedCategory(selectedCategory || null);
  }, [selectedCategory, setStoreSelectedCategory]);

  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.name || categoryId;
  };

  const handleAddSuccess = () => {
    setShowAddDialog(false);
  };

  const handleEditSuccess = () => {
    setEditingItem(null);
  };

  const handleDeleteSuccess = () => {
    setDeletingItem(null);
  };

  return (
    <AdminLayout>
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Globe className="h-8 w-8" />
          网址管理
        </h1>
        <p className="text-muted-foreground mt-2">
          管理网站导航中的所有网址，当前共有 {items.length} 个网址
        </p>
      </div>

      {/* 操作按钮 */}
      <div className="mb-6">
        <AdminActions
          actions={getPageActions(
            () => setShowAddDialog(true),
            () => loadData()
          )}
        />
      </div>

      {/* 搜索和过滤 */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索网址、标题、描述或标签..."
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-48">
              <Select
                value={selectedCategory || 'all'}
                onValueChange={value => setSelectedCategory(value === 'all' ? '' : value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="所有分类" />
                </SelectTrigger>
                <SelectContent className="z-50">
                  <SelectItem value="all">所有分类</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 网址表格 */}
      <DataTable<LinksItem>
        data={filteredItems}
        columns={getTableColumns(getCategoryName)}
        actions={getTableActions(
          record => setEditingItem(record),
          record => setDeletingItem(record)
        )}
      />

      {/* 对话框组件 */}
      <AddDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onSuccess={handleAddSuccess}
      />

      <EditDialog
        item={editingItem}
        onOpenChange={open => !open && setEditingItem(null)}
        onSuccess={handleEditSuccess}
      />

      <DeleteDialog
        item={deletingItem}
        onOpenChange={open => !open && setDeletingItem(null)}
        onSuccess={handleDeleteSuccess}
      />
    </AdminLayout>
  );
}
