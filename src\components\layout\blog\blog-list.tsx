'use client';

import React from 'react';
import { useBlogPosts } from '@/hooks/use-blog';
// import type { BlogPost } from '@/types'; // 移除旧的类型导入
import { BlogCard } from '@/components/common/cards/blog-card';
import { AppGrid } from '@/components/layout/app-grid';

/**
 * 博客列表组件属性
 */
interface BlogListProps {
  /**
   * 最大显示文章数量
   * @default Infinity
   */
  limit?: number;

  /**
   * 按标签筛选
   */
  filterTag?: string | null;

  /**
   * 标签点击处理函数
   */
  onTagClickAction?: (tag: string) => void;
}

/**
 * 博客列表组件
 */
export function BlogList({ limit = Infinity, filterTag = null, onTagClickAction }: BlogListProps) {
  const { posts } = useBlogPosts();

  // 筛选文章
  const filteredPosts = filterTag
    ? posts.filter(post => post.tags && post.tags.includes(filterTag))
    : posts;

  // 限制显示数量
  const displayPosts = limit < Infinity ? filteredPosts.slice(0, limit) : filteredPosts;

  // 筛选后没有文章
  if (displayPosts.length === 0 && filterTag) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground font-medium text-lg">
          没有找到包含标签 "<span className="text-primary font-semibold">{filterTag}</span>" 的文章
        </p>
      </div>
    );
  }

  // 处理标签点击
  const handleTagClick = (tag: string) => {
    if (filterTag === tag) return;
    if (onTagClickAction) {
      onTagClickAction(tag);
    }
  };

  return (
    <AppGrid columns={4}>
      {displayPosts.map(post => (
        <BlogCard
          key={post.slug}
          title={post.title || '无标题'}
          description={post.description || ''}
          href={`/blog/${post.slug}`}
          date={post.createdAt ? new Date(post.createdAt).toLocaleDateString('zh-CN') : undefined}
          tags={post.tags || []}
          onTagClick={handleTagClick}
          image={post.image}
        />
      ))}
    </AppGrid>
  );
}
