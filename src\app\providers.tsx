'use client';

import { ServiceWorkerProvider } from '@/components/providers/service-worker-provider';
import React, { useEffect } from 'react';
import { useThemeStore } from '@/stores/theme.store';

/**
 * A component that watches for theme changes in the Zustand store
 * and applies the theme to the document's root element.
 */
function ThemeWatcher() {
  const { theme } = useThemeStore();

  useEffect(() => {
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(theme);
  }, [theme]);

  return null; // This component does not render anything
}

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <>
      <ThemeWatcher />
      <ServiceWorkerProvider />
      {children}
    </>
  );
}
