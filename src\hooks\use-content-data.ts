'use client';

import { usePathname } from 'next/navigation';
import { ContentOptions, BaseContent, BaseCategory } from '@/types';
import { useCache } from './use-cache';
import { CACHE_CONFIG, HookResult } from '../lib/constants';
import { z } from 'zod';

export type ContentItem = BaseContent;
export type ContentCategory = BaseCategory;

// 请求去重Map
const pendingRequests = new Map<string, Promise<unknown>>();

// 扩展 ContentOptions 以包含 Zod schema
export interface ZodContentOptions<T> extends ContentOptions {
  schema?: z.ZodType<T>;
}

/**
 * 通用内容数据获取钩子 (支持 Zod 验证)
 *
 * @param options 配置选项，可包含一个 Zod schema 用于验证
 * @returns 数据、加载状态和错误信息
 */
export function useContentData<T>({
  type,
  path,
  url,
  category,
  cacheTime = CACHE_CONFIG.DEFAULT_CACHE_TIME,
  disableCache = false,
  params,
  headers,
  schema, // 新增 schema 参数
}: ZodContentOptions<T>): HookResult<T> {
  const pathname = usePathname();

  // 生成缓存key
  const getCacheKey = () => {
    return `${type}:${category || 'all'}:${pathname}`;
  };

  // 数据获取函数
  const fetchData = async () => {
    const apiUrl = url || path || '';
    if (!apiUrl) {
      throw new Error('URL or path is required');
    }

    const requestKey = `${apiUrl}:${JSON.stringify(params)}`;

    if (pendingRequests.has(requestKey)) {
      return pendingRequests.get(requestKey) as Promise<T>;
    }

    const request = (async () => {
      try {
        const response = await fetch(apiUrl, {
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          ...(params || {}),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        // 如果提供了 schema，则使用它来解析数据
        if (schema) {
          return schema.parse(result);
        }

        return result as T;
      } catch (error) {
        console.error('Error fetching or parsing content:', error);
        throw error;
      } finally {
        pendingRequests.delete(requestKey);
      }
    })();

    pendingRequests.set(requestKey, request);
    return request;
  };

  const { data, error, loading, refetch } = useCache<T>(getCacheKey(), fetchData, {
    expiry: disableCache ? 0 : cacheTime,
    useMemoryCache: true,
  });

  return {
    data: data || null,
    loading,
    error: error || null,
    refresh: async () => {
      await refetch();
    },
  };
}

// 分类数据获取hook
export function useCategories(type: 'blog' | 'docs'): HookResult<ContentCategory[]> {
  return useContentData<ContentCategory[]>({
    type,
    path: `/api/${type}/categories`,
  });
}

// 分类内容获取hook
export function useCategoryItems(
  type: 'blog' | 'docs',
  category: string
): HookResult<ContentItem[]> {
  return useContentData<ContentItem[]>({
    type,
    path: `/api/${type}/categories/${encodeURIComponent(category)}`,
  });
}
