import { NextResponse } from 'next/server';
import { getPostsByTag } from '@/lib/content';
import { z } from 'zod';
import { PostSchema } from '@/lib/schemas/blog.schema';

// Zod Schema for route parameters
const ParamsSchema = z.object({
  tag: z.string().min(1, '标签不能为空'),
});

/**
 * 获取指定标签的文章列表的 API 路由
 *
 * @param request 请求对象
 * @param params 路由参数，包含标签名称
 * @returns 包含指定标签的文章列表
 */
export async function GET(request: Request, { params }: { params: Promise<{ tag: string }> }) {
  try {
    const resolvedParams = await params;
    // 1. Validate route parameters
    const validation = ParamsSchema.safeParse(resolvedParams);
    if (!validation.success) {
      return NextResponse.json({ error: validation.error.flatten() }, { status: 400 });
    }

    const { tag } = validation.data;
    const decodedTag = decodeURIComponent(tag);
    const posts = getPostsByTag(decodedTag);

    // 2. Validate the output
    const outputValidation = z.array(PostSchema.partial()).safeParse(posts);
    if (!outputValidation.success) {
      console.error(`标签 "${decodedTag}" 的文章数据验证失败:`, outputValidation.error);
      return NextResponse.json({ error: '服务器数据格式错误' }, { status: 500 });
    }

    return NextResponse.json(outputValidation.data);
  } catch (error) {
    // Use a generic error message to avoid leaking implementation details
    console.error(`获取标签文章列表失败:`, error);
    return NextResponse.json({ error: '获取标签文章列表失败' }, { status: 500 });
  }
}
