import { NextResponse } from 'next/server';
import path from 'path';
import fs from 'fs';
import matter from 'gray-matter';
import { z } from 'zod';
import { PostSchema } from '@/lib/schemas/blog.schema';

// 使用 Zod 推断类型
type BlogPost = z.infer<typeof PostSchema>;

// 获取所有博客文章
function getAllPosts(): Omit<BlogPost, 'id' | 'content'>[] {
  const blogDir = path.join(process.cwd(), 'src', 'content', 'blog');
  if (!fs.existsSync(blogDir)) return [];

  const posts: Omit<BlogPost, 'id' | 'content'>[] = [];

  // 递归函数来查找所有博客文件
  const findPosts = (dir: string) => {
    const items = fs.readdirSync(dir, { withFileTypes: true });

    for (const item of items) {
      const itemPath = path.join(dir, item.name);

      if (item.isDirectory()) {
        findPosts(itemPath);
      } else if (item.isFile() && (item.name.endsWith('.mdx') || item.name.endsWith('.md'))) {
        const fileContent = fs.readFileSync(itemPath, 'utf8');
        const { data } = matter(fileContent);

        // 只包含已发布的文章
        if (data.published !== false) {
          // 计算slug
          let slug = '';
          const relativePath = path.relative(blogDir, itemPath);
          const pathParts = relativePath.split(path.sep);

          if (pathParts.length === 1) {
            slug = pathParts[0].replace(/\.(mdx|md)$/, '');
          } else {
            const fileName = pathParts.pop() || '';
            slug = `${pathParts.join('/')}/${fileName.replace(/\.(mdx|md)$/, '')}`;
          }

          posts.push({
            slug,
            title: data.title || slug,
            description: data.description || '暂无描述',
            excerpt: data.excerpt || '点击阅读全文', // 添加 excerpt
            createdAt: data.date,
            tags: data.tags || [],
            category: data.category || '未分类',
            image: data.image,
          });
        }
      }
    }
  };

  findPosts(blogDir);

  // 按日期排序
  return posts.sort((a, b) => {
    if (a.createdAt && b.createdAt) {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
    return 0;
  });
}

export async function GET() {
  try {
    const postsData = getAllPosts();

    // 使用 Zod schema 验证输出数据
    const validationResult = z.array(PostSchema.partial()).safeParse(postsData);

    if (!validationResult.success) {
      console.error('博客文章数据验证失败:', validationResult.error);
      return NextResponse.json({ error: '服务器数据格式错误' }, { status: 500 });
    }

    // 设置缓存控制头，避免浏览器缓存
    return NextResponse.json(validationResult.data, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
      },
    });
  } catch (error) {
    console.error('获取博客文章列表失败:', error);
    return NextResponse.json({ error: '获取博客文章列表失败' }, { status: 500 });
  }
}
