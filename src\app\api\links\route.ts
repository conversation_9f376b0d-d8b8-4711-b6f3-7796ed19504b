import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { nanoid } from 'nanoid';
import {
  LinksItem,
  LinksFormData,
  LinksData,
  LinksFormDataSchema,
  validateLinksFormData,
  validateLinksItem,
  LinksDataSchema,
} from '@/lib/schemas/links.schema';
import { createApiHandler } from '@/lib/api-validator';

const itemsFilePath = path.join(process.cwd(), 'src/data/links/items.json');
const categoriesFilePath = path.join(process.cwd(), 'src/data/links/categories.json');

// 读取全部 items
async function readItems(): Promise<LinksItem[]> {
  try {
    const data = await fs.readFile(itemsFilePath, 'utf-8');
    const items = JSON.parse(data);
    const validation = LinksDataSchema.pick({ items: true }).safeParse({ items });
    if (!validation.success) {
      console.error('Items data validation failed:', validation.error);
      return [];
    }
    return validation.data.items;
  } catch (error) {
    console.error('Error reading items:', error);
    return [];
  }
}

// 写入全部 items
async function writeItems(items: LinksItem[]): Promise<void> {
  try {
    await fs.writeFile(itemsFilePath, JSON.stringify(items, null, 2), 'utf-8');
  } catch (error) {
    console.error('Error writing items:', error);
    throw new Error('Failed to save items');
  }
}

// 读取全部 categories
async function readCategories() {
  try {
    const data = await fs.readFile(categoriesFilePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading categories:', error);
    return [];
  }
}

export async function GET() {
  try {
    const [items, categories] = await Promise.all([readItems(), readCategories()]);

    const linksData: LinksData = {
      items,
      categories,
    };

    return NextResponse.json(linksData);
  } catch (error) {
    console.error('GET /api/links error:', error);
    return NextResponse.json({ error: 'Failed to read links data' }, { status: 500 });
  }
}

export const POST = createApiHandler(LinksFormDataSchema, async (request, validatedData) => {
  try {
    const formData = validatedData as LinksFormData;
    const items = await readItems();

    // 检查 URL 是否已存在
    if (items.some(item => item.url === formData.url)) {
      return NextResponse.json({ error: 'URL already exists' }, { status: 409 });
    }

    const now = new Date().toISOString();
    const newItem: LinksItem = {
      ...formData,
      id: nanoid(),
      createdAt: now,
      updatedAt: now,
      isActive: true,
    };

    // 验证新创建的项目
    const itemValidation = validateLinksItem(newItem);
    if (!itemValidation.success) {
      console.error('Created item validation failed:', itemValidation.error);
      return NextResponse.json({ error: 'Invalid item data' }, { status: 400 });
    }

    items.push(itemValidation.data);
    await writeItems(items);

    return NextResponse.json(itemValidation.data, { status: 201 });
  } catch (error) {
    console.error('POST /api/links error:', error);
    return NextResponse.json({ error: 'Failed to add item' }, { status: 500 });
  }
});

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }

    const requestBody = await request.json();

    // 验证更新数据（部分字段）
    const updateValidation = LinksFormDataSchema.partial().safeParse(requestBody);
    if (!updateValidation.success) {
      return NextResponse.json(
        {
          error: 'Invalid update data',
          details: updateValidation.error.errors,
        },
        { status: 400 }
      );
    }

    const updates = updateValidation.data;
    const items = await readItems();
    const itemIndex = items.findIndex(item => item.id === id);

    if (itemIndex === -1) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 });
    }

    // 检查 URL 是否重复（如果更新了 URL）
    if (updates.url && items.some(item => item.url === updates.url && item.id !== id)) {
      return NextResponse.json({ error: 'URL already exists' }, { status: 409 });
    }

    const now = new Date().toISOString();
    const updatedItem: LinksItem = {
      ...items[itemIndex],
      ...updates,
      updatedAt: now,
    };

    // 验证更新后的完整项目
    const itemValidation = validateLinksItem(updatedItem);
    if (!itemValidation.success) {
      console.error('Updated item validation failed:', itemValidation.error);
      return NextResponse.json({ error: 'Invalid updated item data' }, { status: 400 });
    }

    items[itemIndex] = itemValidation.data;
    await writeItems(items);

    return NextResponse.json(itemValidation.data);
  } catch (error) {
    console.error('PUT /api/links error:', error);
    return NextResponse.json({ error: 'Failed to update item' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }

    const items = await readItems();
    const itemIndex = items.findIndex(item => item.id === id);

    if (itemIndex === -1) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 });
    }

    // 移除项目
    const deletedItem = items.splice(itemIndex, 1)[0];
    await writeItems(items);

    return NextResponse.json({
      success: true,
      deletedItem: {
        id: deletedItem.id,
        title: deletedItem.title,
      },
    });
  } catch (error) {
    console.error('DELETE /api/links error:', error);
    return NextResponse.json({ error: 'Failed to delete item' }, { status: 500 });
  }
}
