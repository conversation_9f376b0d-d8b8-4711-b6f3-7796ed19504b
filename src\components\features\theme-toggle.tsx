'use client';

import * as React from 'react';
import { useThemeStore } from '@/stores/theme.store';
import { Button } from '@/components/ui/button';
import { Sun, Moon } from 'lucide-react';

const ICON_SIZE = {
  height: '1rem',
  width: '1rem',
} as const;

/**
 * 主题切换组件属性
 */
export interface ThemeToggleProps {
  /**
   * 是否显示文本标签
   * @default false
   */
  showLabel?: boolean;
}

/**
 * 主题切换组件
 * 用于切换明暗主题模式，状态由 Zustand 管理
 */
export function ThemeToggle({ showLabel = false }: ThemeToggleProps = {}) {
  // 从 Zustand store 获取主题状态和切换函数
  const { theme, toggleTheme } = useThemeStore();

  // 根据当前主题确定图标和标签
  const isDark = theme === 'dark';
  const IconComponent = isDark ? Sun : Moon;
  const label = isDark ? '切换到浅色模式' : '切换到暗黑模式';

  return (
    <Button
      variant="ghost"
      size={showLabel ? 'default' : 'icon'}
      className={
        showLabel
          ? 'gap-2 text-muted-foreground hover:text-foreground'
          : 'h-9 w-9 text-muted-foreground hover:text-foreground'
      }
      title={label}
      aria-label={label}
      onClick={toggleTheme}
    >
      <div className="flex items-center justify-center">
        <IconComponent style={ICON_SIZE} />
      </div>

      {showLabel && <span className="hidden sm:inline-block">{label}</span>}
    </Button>
  );
}

/**
 * @deprecated 请使用 ThemeToggle 替代 ModeToggle，ModeToggle 将在未来版本中移除
 */
export { ThemeToggle as ModeToggle };
