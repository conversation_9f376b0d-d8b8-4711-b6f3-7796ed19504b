import { z } from 'zod';

/**
 * 链接分类 ID 枚举
 */
export const CategoryIdSchema = z.enum([
  'ai',
  'development',
  'design',
  'audio',
  'video',
  'office',
  'productivity',
  'operation',
  'profile',
  'friends',
]);

/**
 * 图标类型枚举
 */
export const IconTypeSchema = z.enum(['image', 'emoji', 'text']);

/**
 * 基础链接分类模式（基于实际数据文件结构）
 */
export const LinksCategorySchema = z.object({
  /** 分类 ID */
  id: CategoryIdSchema,
  /** 分类名称 */
  name: z.string().min(1, '分类名称不能为空'),
  /** 分类描述 */
  description: z.string(),
  /** 排序顺序 */
  order: z.number().int().min(0),
});

/**
 * 链接项模式（基于实际数据文件结构）
 */
export const LinksItemSchema = z.object({
  /** 唯一标识 */
  id: z.string().min(1, 'ID 不能为空'),
  /** 标题 */
  title: z.string().min(1, '标题不能为空').max(100, '标题不能超过100个字符'),
  /** 描述 */
  description: z.string().max(500, '描述不能超过500个字符'),
  /** 链接地址 */
  url: z.string().url('请输入有效的 URL 地址'),
  /** 图标 */
  icon: z.string(),
  /** 图标类型 */
  iconType: IconTypeSchema,
  /** 标签列表 */
  tags: z.array(z.string()),
  /** 是否为特色链接 */
  featured: z.boolean(),
  /** 所属分类 */
  category: CategoryIdSchema,
  /** 创建时间 */
  createdAt: z.string().datetime(),
  /** 更新时间 */
  updatedAt: z.string().datetime(),
});

/**
 * 创建链接项模式（不包含 id、createdAt、updatedAt）
 */
export const CreateLinksItemSchema = LinksItemSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * 更新链接项模式（所有字段都是可选的，除了 updatedAt）
 */
export const UpdateLinksItemSchema = LinksItemSchema.partial().extend({
  updatedAt: z.string().datetime(),
});

/**
 * 链接表单数据模式
 */
export const LinksFormDataSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(100, '标题不能超过100个字符'),
  description: z.string().max(500, '描述不能超过500个字符').default(''),
  url: z.string().url('请输入有效的 URL 地址'),
  icon: z.string().default(''),
  iconType: IconTypeSchema.default('image'),
  tags: z.array(z.string()).default([]),
  featured: z.boolean().default(false),
  category: CategoryIdSchema,
});

/**
 * 链接数据集合模式
 */
export const LinksDataSchema = z.object({
  categories: z.array(LinksCategorySchema),
  items: z.array(LinksItemSchema),
});

/**
 * 网站元数据模式
 */
export const WebsiteMetadataSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  icon: z.string().optional(),
  image: z.string().optional(),
});

/**
 * API 查询参数模式
 */
export const LinksQuerySchema = z.object({
  category: CategoryIdSchema.optional(),
  tag: z.string().optional(),
  featured: z.boolean().optional(),
  search: z.string().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
});

/**
 * API 响应模式
 */
export const LinksApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
});

/**
 * 分页响应模式
 */
export const PaginatedLinksResponseSchema = z.object({
  items: z.array(LinksItemSchema),
  total: z.number().int().min(0),
  page: z.number().int().min(1),
  limit: z.number().int().min(1),
  totalPages: z.number().int().min(0),
});

// 导出类型推断
export type CategoryId = z.infer<typeof CategoryIdSchema>;
export type IconType = z.infer<typeof IconTypeSchema>;
export type LinksCategory = z.infer<typeof LinksCategorySchema>;
export type LinksItem = z.infer<typeof LinksItemSchema>;
export type CreateLinksItem = z.infer<typeof CreateLinksItemSchema>;
export type UpdateLinksItem = z.infer<typeof UpdateLinksItemSchema>;
export type LinksFormData = z.infer<typeof LinksFormDataSchema>;
export type LinksData = z.infer<typeof LinksDataSchema>;
export type WebsiteMetadata = z.infer<typeof WebsiteMetadataSchema>;
export type LinksQuery = z.infer<typeof LinksQuerySchema>;
export type LinksApiResponse = z.infer<typeof LinksApiResponseSchema>;
export type PaginatedLinksResponse = z.infer<typeof PaginatedLinksResponseSchema>;

/**
 * 验证工具函数
 */
export const validateLinksItem = (data: unknown) => {
  return LinksItemSchema.safeParse(data);
};

export const validateCreateLinksItem = (data: unknown) => {
  return CreateLinksItemSchema.safeParse(data);
};

export const validateLinksFormData = (data: unknown) => {
  return LinksFormDataSchema.safeParse(data);
};

export const validateLinksCategory = (data: unknown) => {
  return LinksCategorySchema.safeParse(data);
};

export const validateLinksData = (data: unknown) => {
  return LinksDataSchema.safeParse(data);
};

/**
 * 数据转换工具函数
 */
export const transformToLinksItem = (formData: LinksFormData): CreateLinksItem => {
  return CreateLinksItemSchema.parse(formData);
};

export const transformToFormData = (item: LinksItem): LinksFormData => {
  return LinksFormDataSchema.parse({
    title: item.title,
    description: item.description,
    url: item.url,
    icon: item.icon,
    iconType: item.iconType,
    tags: item.tags,
    featured: item.featured,
    category: item.category,
  });
};

/**
 * 默认值
 */
export const DEFAULT_LINKS_ITEM: Partial<CreateLinksItem> = {
  description: '',
  icon: '',
  iconType: 'image',
  tags: [],
  featured: false,
};

export const DEFAULT_LINKS_CATEGORY: Partial<LinksCategory> = {
  order: 0,
  description: '',
};
