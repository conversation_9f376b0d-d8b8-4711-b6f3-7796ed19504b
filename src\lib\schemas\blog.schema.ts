import { z } from 'zod';

export const PostSchema = z.object({
  id: z.string(),
  title: z.string(),
  content: z.string(),
  slug: z.string(),
  tags: z.array(z.string()),
  createdAt: z.string().datetime(),
  description: z.string().optional(),
  image: z.string().optional(),
  category: z.string().optional(),
  excerpt: z.string().optional(), // 添加 excerpt
});

export const CreatePostSchema = PostSchema.omit({ id: true, createdAt: true });
